/**
 * Get Discovery Status - Get current discovery status from BMSConnector table
 */
module.exports = {
  friendlyName: 'Get Discovery Status',
  description: 'Get current discovery status and button states from BMSConnector table',

  inputs: {
    // _userMeta: {
    //   type: 'json',
    //   required: true,
    //   example: { id: 'userId', _role: 'role', _site: 'siteId' },
    //   description: 'User meta information added by default to authenticated routes'
    // },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },

    slaveControllerId: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    }
  },

  exits: {
    success: {
      statusCode: 200,
    },
    badRequest: {
      statusCode: 400,
    },
    notFound: {
      statusCode: 404,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { siteId, slaveControllerId } = inputs;

    try {
      // Get BMS connector with discovery status
      const bmsConnectorDetail = await BMSConnector.findOne({
        siteId: siteId,
        slaveControllerId: parseInt(slaveControllerId),
        status: 1
      });

      if (!bmsConnectorDetail) {
        return exits.notFound({
          message: "BMS connector not found for the given slave controller",
        });
      }

      // Count discovered devices
      const discoveredDevicesCount = await BMSDevice.count({
        bmsConnectorRefId: bmsConnectorDetail.id,
        status: 1,
      });

      // Map status numbers to readable strings
      const statusMap = {
        0: 'idle',
        1: 'discovering',
        2: 'completed',
        3: 'failed'
      };

      const discoveryStatus = statusMap[bmsConnectorDetail.discoveryStatus] || 'idle';
      const isDiscovering = bmsConnectorDetail.discoveryStatus === 1;
      const hasInitialSync = bmsConnectorDetail.lastSyncTs !== null;

      const response = {
        discoveryStatus: discoveryStatus,
        lastSyncTs: bmsConnectorDetail.lastSyncTs,
        requestId: bmsConnectorDetail.discoveryRequestId,
        discoveredDevicesCount: discoveredDevicesCount,
        isDiscovering: isDiscovering,
        canConfigure: hasInitialSync && discoveredDevicesCount > 0
      };

      return exits.success(response);

    } catch (error) {
      sails.log.error('[BMS > get-discovery-status] Error:', error);

      return exits.serverError({
        message: 'Failed to get discovery status',
        error: error.message
      });
    }
  }
};
