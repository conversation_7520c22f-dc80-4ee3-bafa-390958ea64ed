module.exports = {
  PARAM_TYPE: {
    data: "data",
    command: "command",
  },
  FUNCTION_TYPE: {
    read: "read",
    write: "write",
    both: "both",
  },
  ERROR_CODE: {
    INVALID_DRIVER_ID: {
      errorCode: "INVALID_DRIVER_ID",
      errorMessage: `The driver ID provided either does not exist in the system or does not belong to the component class. Please ensure that the driver ID is valid and corresponds to the only component class. In case of driver exist in "devicetype" table of dynamodb, check that driver class should be "components"`,
      code: 10026,
    },
  },
};
