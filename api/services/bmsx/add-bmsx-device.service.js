const constants = require("../../utils/bmsx/constants");

function updateParametersArray(parameters) {
  if (parameters && Array.isArray(parameters)) {
    const paramTypes = new Set();
    let hasMissingType = false;

    const updatedParameters = parameters.map((param) => {
      //check if all the param has type field
      if (!param || !param.hasOwnProperty("type")) {
        hasMissingType = true;
      }

      let updatedParam = param;
      if (param && typeof param.matchParam === "object" && param.matchParam !== null) {
        updatedParam = {
          ...param,
          matchParam: JSON.stringify(param.matchParam),
        };
      }
      if (updatedParam && updatedParam.type) {
        paramTypes.add(updatedParam.type);
      }
      return updatedParam;
    });

    if (hasMissingType) {
      return {
        error: "Please add valid parameters.",
      };
    }

    let functionType = constants.FUNCTION_TYPE.read;
    if (paramTypes.size === 0) {
      return {
        error: "Please add valid parameters.",
      };
    }
    if (paramTypes.size === 1) {
      const onlyType = Array.from(paramTypes)[0];
      if (onlyType === constants.PARAM_TYPE.data) {
        functionType = constants.FUNCTION_TYPE.read;
      } else if (onlyType === constants.PARAM_TYPE.command) {
        functionType = constants.FUNCTION_TYPE.write;
      }
    } else if (
      paramTypes.has(constants.PARAM_TYPE.data) &&
      paramTypes.has(constants.PARAM_TYPE.command)
    ) {
      functionType = constants.FUNCTION_TYPE.both;
    }

    return {
      parameters: updatedParameters,
      functionType: functionType,
    };
  }
}

module.exports = {
  updateParametersArray,
};
